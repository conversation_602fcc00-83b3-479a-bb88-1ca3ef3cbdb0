'use client';

import { TabsList } from '@telegram-apps/telegram-ui';
import { TabsItem } from '@telegram-apps/telegram-ui/dist/components/Navigation/TabsList/components/TabsItem/TabsItem';

export type MyOrdersTabType = 'buy' | 'sell';

interface MyOrdersTabsProps {
  activeTab: MyOrdersTabType;
  onTabChange: (tab: MyOrdersTabType) => void;
  buyOrdersCount: number;
  sellOrdersCount: number;
}

export const MyOrdersTabs = ({
  activeTab,
  onTabChange,
  buyOrdersCount,
  sellOrdersCount,
}: MyOrdersTabsProps) => {
  return (
    <TabsList className="grid w-full grid-cols-2 gap-0!">
      <TabsItem
        selected={activeTab === 'buy'}
        onClick={() => onTabChange('buy')}
      >
        My Buy Orders ({buyOrdersCount})
      </TabsItem>
      <TabsItem
        selected={activeTab === 'sell'}
        onClick={() => onTabChange('sell')}
      >
        My Sell Orders ({sellOrdersCount})
      </TabsItem>
    </TabsList>
  );
};
