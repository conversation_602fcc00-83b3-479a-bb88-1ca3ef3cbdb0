import type { CollectionEntity } from '@/constants/core.constants';

interface CollectionNameProps {
  collection: CollectionEntity | null | undefined;
  className?: string;
  fallback?: string;
}

export function CollectionName({
  collection,
  className = "",
  fallback = "Unknown Collection",
}: CollectionNameProps) {
  return (
    <span className={className}>
      {collection?.name || fallback}
    </span>
  );
}
