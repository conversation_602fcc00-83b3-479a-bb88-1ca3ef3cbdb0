'use client';

import { useLocalStorage } from 'usehooks-ts';

import { TgsOrImage } from '@/components/TgsOrImage';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';
import { LocalStorageKeys } from '@/constants/storage.constants';

interface OrderImageProps {
  order: OrderEntity;
  collection: CollectionEntity | null | undefined;
  className?: string;
  children?: React.ReactNode;
}

export function OrderImage({
  order,
  collection,
  className = "aspect-square relative rounded-lg overflow-hidden bg-[#17212b]",
  children,
}: OrderImageProps) {
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  return (
    <div className={className}>
      <TgsOrImage
        isImage={!isAnimatedCollection}
        collectionId={order.collectionId}
        imageProps={{
          alt: collection?.name || 'Order item',
          fill: true,
          className:
            'object-cover group-hover:scale-105 transition-transform duration-200',
        }}
        tgsProps={{
          style: { height: 'auto', width: 'auto', padding: '16px' },
        }}
      />
      {children}
    </div>
  );
}
